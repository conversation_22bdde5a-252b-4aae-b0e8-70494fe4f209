# NOTE: Requires **VS2019 16.7** or later

# Rules from '9.0' release with 'Recommended' analysis mode
# Description: Rules with enabled-by-default state from '9.0' release with 'Recommended' analysis mode. Rules that are first released in a version later than '9.0' are disabled.

is_global = true

global_level = -100


# CA1000: Do not declare static members on generic types
dotnet_diagnostic.CA1000.severity = warning

# CA1001: Types that own disposable fields should be disposable
dotnet_diagnostic.CA1001.severity = warning

# CA1010: Generic interface should also be implemented
dotnet_diagnostic.CA1010.severity = warning

# CA1016: Mark assemblies with assembly version
dotnet_diagnostic.CA1016.severity = warning

# CA1018: Mark attributes with AttributeUsageAttribute
dotnet_diagnostic.CA1018.severity = warning

# CA1036: Override methods on comparable types
dotnet_diagnostic.CA1036.severity = warning

# CA1041: Provide ObsoleteAttribute message
dotnet_diagnostic.CA1041.severity = warning

# CA1047: Do not declare protected member in sealed type
dotnet_diagnostic.CA1047.severity = warning

# CA1050: Declare types in namespaces
dotnet_diagnostic.CA1050.severity = warning

# CA1051: Do not declare visible instance fields
dotnet_diagnostic.CA1051.severity = warning

# CA1061: Do not hide base class methods
dotnet_diagnostic.CA1061.severity = warning

# CA1067: Override Object.Equals(object) when implementing IEquatable<T>
dotnet_diagnostic.CA1067.severity = warning

# CA1068: CancellationToken parameters must come last
dotnet_diagnostic.CA1068.severity = warning

# CA1069: Enums values should not be duplicated
dotnet_diagnostic.CA1069.severity = warning

# CA1070: Do not declare event fields as virtual
dotnet_diagnostic.CA1070.severity = warning

# CA1200: Avoid using cref tags with a prefix
dotnet_diagnostic.CA1200.severity = warning

# CA1304: Specify CultureInfo
dotnet_diagnostic.CA1304.severity = warning

# CA1305: Specify IFormatProvider
dotnet_diagnostic.CA1305.severity = warning

# CA1309: Use ordinal string comparison
dotnet_diagnostic.CA1309.severity = warning

# CA1310: Specify StringComparison for correctness
dotnet_diagnostic.CA1310.severity = warning

# CA1311: Specify a culture or use an invariant version
dotnet_diagnostic.CA1311.severity = warning

# CA1401: P/Invokes should not be visible
dotnet_diagnostic.CA1401.severity = warning

# CA1419: Provide a parameterless constructor that is as visible as the containing type for concrete types derived from 'System.Runtime.InteropServices.SafeHandle'
dotnet_diagnostic.CA1419.severity = warning

# CA1421: This method uses runtime marshalling even when the 'DisableRuntimeMarshallingAttribute' is applied
dotnet_diagnostic.CA1421.severity = warning

# CA1507: Use nameof to express symbol names
dotnet_diagnostic.CA1507.severity = warning

# CA1510: Use ArgumentNullException throw helper
dotnet_diagnostic.CA1510.severity = warning

# CA1511: Use ArgumentException throw helper
dotnet_diagnostic.CA1511.severity = warning

# CA1512: Use ArgumentOutOfRangeException throw helper
dotnet_diagnostic.CA1512.severity = warning

# CA1513: Use ObjectDisposedException throw helper
dotnet_diagnostic.CA1513.severity = warning

# CA1514: Avoid redundant length argument
dotnet_diagnostic.CA1514.severity = warning

# CA1707: Identifiers should not contain underscores
dotnet_diagnostic.CA1707.severity = warning

# CA1708: Identifiers should differ by more than case
dotnet_diagnostic.CA1708.severity = warning

# CA1710: Identifiers should have correct suffix
dotnet_diagnostic.CA1710.severity = warning

# CA1711: Identifiers should not have incorrect suffix
dotnet_diagnostic.CA1711.severity = warning

# CA1712: Do not prefix enum values with type name
dotnet_diagnostic.CA1712.severity = warning

# CA1715: Identifiers should have correct prefix
dotnet_diagnostic.CA1715.severity = warning

# CA1716: Identifiers should not match keywords
dotnet_diagnostic.CA1716.severity = warning

# CA1720: Identifier contains type name
dotnet_diagnostic.CA1720.severity = warning

# CA1725: Parameter names should match base declaration
dotnet_diagnostic.CA1725.severity = warning

# CA1727: Use PascalCase for named placeholders
dotnet_diagnostic.CA1727.severity = warning

# CA1805: Do not initialize unnecessarily
dotnet_diagnostic.CA1805.severity = warning

# CA1806: Do not ignore method results
dotnet_diagnostic.CA1806.severity = warning

# CA1816: Dispose methods should call SuppressFinalize
dotnet_diagnostic.CA1816.severity = warning

# CA1821: Remove empty Finalizers
dotnet_diagnostic.CA1821.severity = warning

# CA1822: Mark members as static
dotnet_diagnostic.CA1822.severity = warning

# CA1824: Mark assemblies with NeutralResourcesLanguageAttribute
dotnet_diagnostic.CA1824.severity = warning

# CA1825: Avoid zero-length array allocations
dotnet_diagnostic.CA1825.severity = warning

# CA1826: Do not use Enumerable methods on indexable collections
dotnet_diagnostic.CA1826.severity = warning

# CA1827: Do not use Count() or LongCount() when Any() can be used
dotnet_diagnostic.CA1827.severity = warning

# CA1828: Do not use CountAsync() or LongCountAsync() when AnyAsync() can be used
dotnet_diagnostic.CA1828.severity = warning

# CA1829: Use Length/Count property instead of Count() when available
dotnet_diagnostic.CA1829.severity = warning

# CA1830: Prefer strongly-typed Append and Insert method overloads on StringBuilder
dotnet_diagnostic.CA1830.severity = warning

# CA1832: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1832.severity = warning

# CA1833: Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1833.severity = warning

# CA1834: Consider using 'StringBuilder.Append(char)' when applicable
dotnet_diagnostic.CA1834.severity = warning

# CA1835: Prefer the 'Memory'-based overloads for 'ReadAsync' and 'WriteAsync'
dotnet_diagnostic.CA1835.severity = warning

# CA1836: Prefer IsEmpty over Count
dotnet_diagnostic.CA1836.severity = warning

# CA1837: Use 'Environment.ProcessId'
dotnet_diagnostic.CA1837.severity = warning

# CA1838: Avoid 'StringBuilder' parameters for P/Invokes
dotnet_diagnostic.CA1838.severity = warning

# CA1839: Use 'Environment.ProcessPath'
dotnet_diagnostic.CA1839.severity = warning

# CA1840: Use 'Environment.CurrentManagedThreadId'
dotnet_diagnostic.CA1840.severity = warning

# CA1841: Prefer Dictionary.Contains methods
dotnet_diagnostic.CA1841.severity = warning

# CA1842: Do not use 'WhenAll' with a single task
dotnet_diagnostic.CA1842.severity = warning

# CA1843: Do not use 'WaitAll' with a single task
dotnet_diagnostic.CA1843.severity = warning

# CA1844: Provide memory-based overrides of async methods when subclassing 'Stream'
dotnet_diagnostic.CA1844.severity = warning

# CA1845: Use span-based 'string.Concat'
dotnet_diagnostic.CA1845.severity = warning

# CA1846: Prefer 'AsSpan' over 'Substring'
dotnet_diagnostic.CA1846.severity = warning

# CA1847: Use char literal for a single character lookup
dotnet_diagnostic.CA1847.severity = warning

# CA1848: Use the LoggerMessage delegates
dotnet_diagnostic.CA1848.severity = warning

# CA1850: Prefer static 'HashData' method over 'ComputeHash'
dotnet_diagnostic.CA1850.severity = warning

# CA1852: Seal internal types
dotnet_diagnostic.CA1852.severity = warning

# CA1853: Unnecessary call to 'Dictionary.ContainsKey(key)'
dotnet_diagnostic.CA1853.severity = warning

# CA1854: Prefer the 'IDictionary.TryGetValue(TKey, out TValue)' method
dotnet_diagnostic.CA1854.severity = warning

# CA1855: Prefer 'Clear' over 'Fill'
dotnet_diagnostic.CA1855.severity = warning

# CA1856: Incorrect usage of ConstantExpected attribute
dotnet_diagnostic.CA1856.severity = warning

# CA1858: Use 'StartsWith' instead of 'IndexOf'
dotnet_diagnostic.CA1858.severity = warning

# CA1859: Use concrete types when possible for improved performance
dotnet_diagnostic.CA1859.severity = warning

# CA1860: Avoid using 'Enumerable.Any()' extension method
dotnet_diagnostic.CA1860.severity = warning

# CA1861: Avoid constant arrays as arguments
dotnet_diagnostic.CA1861.severity = warning

# CA1862: Use the 'StringComparison' method overloads to perform case-insensitive string comparisons
dotnet_diagnostic.CA1862.severity = warning

# CA1863: Use 'CompositeFormat'
dotnet_diagnostic.CA1863.severity = warning

# CA1864: Prefer the 'IDictionary.TryAdd(TKey, TValue)' method
dotnet_diagnostic.CA1864.severity = warning

# CA1865: Use char overload
dotnet_diagnostic.CA1865.severity = warning

# CA1866: Use char overload
dotnet_diagnostic.CA1866.severity = warning

# CA1868: Unnecessary call to 'Contains(item)'
dotnet_diagnostic.CA1868.severity = warning

# CA1869: Cache and reuse 'JsonSerializerOptions' instances
dotnet_diagnostic.CA1869.severity = warning

# CA1870: Use a cached 'SearchValues' instance
dotnet_diagnostic.CA1870.severity = warning

# CA1871: Do not pass a nullable struct to 'ArgumentNullException.ThrowIfNull'
dotnet_diagnostic.CA1871.severity = warning

# CA1872: Prefer 'Convert.ToHexString' and 'Convert.ToHexStringLower' over call chains based on 'BitConverter.ToString'
dotnet_diagnostic.CA1872.severity = warning

# CA2009: Do not call ToImmutableCollection on an ImmutableCollection value
dotnet_diagnostic.CA2009.severity = warning

# CA2011: Avoid infinite recursion
dotnet_diagnostic.CA2011.severity = warning

# CA2012: Use ValueTasks correctly
dotnet_diagnostic.CA2012.severity = warning

# CA2016: Forward the 'CancellationToken' parameter to methods
dotnet_diagnostic.CA2016.severity = warning

# CA2019: Improper 'ThreadStatic' field initialization
dotnet_diagnostic.CA2019.severity = warning

# CA2020: Prevent behavioral change
dotnet_diagnostic.CA2020.severity = warning

# CA2101: Specify marshaling for P/Invoke string arguments
dotnet_diagnostic.CA2101.severity = warning

# CA2201: Do not raise reserved exception types
dotnet_diagnostic.CA2201.severity = warning

# CA2208: Instantiate argument exceptions correctly
dotnet_diagnostic.CA2208.severity = warning

# CA2211: Non-constant fields should not be visible
dotnet_diagnostic.CA2211.severity = warning

# CA2215: Dispose methods should call base class dispose
dotnet_diagnostic.CA2215.severity = warning

# CA2218: Override GetHashCode on overriding Equals
dotnet_diagnostic.CA2218.severity = warning

# CA2219: Do not raise exceptions in finally clauses
dotnet_diagnostic.CA2219.severity = warning

# CA2224: Override Equals on overloading operator equals
dotnet_diagnostic.CA2224.severity = warning

# CA2231: Overload operator equals on overriding value type Equals
dotnet_diagnostic.CA2231.severity = warning

# CA2241: Provide correct arguments to formatting methods
dotnet_diagnostic.CA2241.severity = warning

# CA2242: Test for NaN correctly
dotnet_diagnostic.CA2242.severity = warning

# CA2244: Do not duplicate indexed element initializations
dotnet_diagnostic.CA2244.severity = warning

# CA2245: Do not assign a property to itself
dotnet_diagnostic.CA2245.severity = warning

# CA2246: Assigning symbol and its member in the same statement
dotnet_diagnostic.CA2246.severity = warning

# CA2248: Provide correct 'enum' argument to 'Enum.HasFlag'
dotnet_diagnostic.CA2248.severity = warning

# CA2249: Consider using 'string.Contains' instead of 'string.IndexOf'
dotnet_diagnostic.CA2249.severity = warning

# CA2250: Use 'ThrowIfCancellationRequested'
dotnet_diagnostic.CA2250.severity = warning

# CA2251: Use 'string.Equals'
dotnet_diagnostic.CA2251.severity = warning

# CA2252: This API requires opting into preview features
dotnet_diagnostic.CA2252.severity = warning

# CA2253: Named placeholders should not be numeric values
dotnet_diagnostic.CA2253.severity = warning

# CA2254: Template should be a static expression
dotnet_diagnostic.CA2254.severity = warning

# CA2262: Set 'MaxResponseHeadersLength' properly
dotnet_diagnostic.CA2262.severity = warning

# CA2263: Prefer generic overload when type is known
dotnet_diagnostic.CA2263.severity = warning

# CA3061: Do Not Add Schema By URL
dotnet_diagnostic.CA3061.severity = warning

# CA3075: Insecure DTD processing in XML
dotnet_diagnostic.CA3075.severity = warning

# CA3076: Insecure XSLT script processing
dotnet_diagnostic.CA3076.severity = warning

# CA3077: Insecure Processing in API Design, XmlDocument and XmlTextReader
dotnet_diagnostic.CA3077.severity = warning

# CA3147: Mark Verb Handlers With Validate Antiforgery Token
dotnet_diagnostic.CA3147.severity = warning

# CA5350: Do Not Use Weak Cryptographic Algorithms
dotnet_diagnostic.CA5350.severity = warning

# CA5351: Do Not Use Broken Cryptographic Algorithms
dotnet_diagnostic.CA5351.severity = warning

# CA5359: Do Not Disable Certificate Validation
dotnet_diagnostic.CA5359.severity = warning

# CA5360: Do Not Call Dangerous Methods In Deserialization
dotnet_diagnostic.CA5360.severity = warning

# CA5363: Do Not Disable Request Validation
dotnet_diagnostic.CA5363.severity = warning

# CA5364: Do Not Use Deprecated Security Protocols
dotnet_diagnostic.CA5364.severity = warning

# CA5365: Do Not Disable HTTP Header Checking
dotnet_diagnostic.CA5365.severity = warning

# CA5366: Use XmlReader for 'DataSet.ReadXml()'
dotnet_diagnostic.CA5366.severity = warning

# CA5368: Set ViewStateUserKey For Classes Derived From Page
dotnet_diagnostic.CA5368.severity = warning

# CA5369: Use XmlReader for 'XmlSerializer.Deserialize()'
dotnet_diagnostic.CA5369.severity = warning

# CA5370: Use XmlReader for XmlValidatingReader constructor
dotnet_diagnostic.CA5370.severity = warning

# CA5371: Use XmlReader for 'XmlSchema.Read()'
dotnet_diagnostic.CA5371.severity = warning

# CA5372: Use XmlReader for XPathDocument constructor
dotnet_diagnostic.CA5372.severity = warning

# CA5373: Do not use obsolete key derivation function
dotnet_diagnostic.CA5373.severity = warning

# CA5374: Do Not Use XslTransform
dotnet_diagnostic.CA5374.severity = warning

# CA5379: Ensure Key Derivation Function algorithm is sufficiently strong
dotnet_diagnostic.CA5379.severity = warning

# CA5384: Do Not Use Digital Signature Algorithm (DSA)
dotnet_diagnostic.CA5384.severity = warning

# CA5385: Use Rivest-Shamir-Adleman (RSA) Algorithm With Sufficient Key Size
dotnet_diagnostic.CA5385.severity = warning

# CA5397: Do not use deprecated SslProtocols values
dotnet_diagnostic.CA5397.severity = warning
