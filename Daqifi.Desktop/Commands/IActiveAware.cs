// Copyright (c) Microsoft Corporation. All rights reserved. See License.txt in the project root for license information.

namespace Daqifi.Desktop.Commands;

/// <summary>
/// Interface that defines if the object instance is active
/// and notifies when the activity changes.
/// </summary>
public interface IActiveAware
{
    /// <summary>
    /// Gets or sets a value indicating whether the object is active.
    /// </summary>
    /// <value><see langword="true" /> if the object is active; otherwise <see langword="false" />.</value>
    bool IsActive { get; set; }

    /// <summary>
    /// Notifies that the value for <see cref="IsActive"/> property has changed.
    /// </summary>
    event EventHandler IsActiveChanged;
}