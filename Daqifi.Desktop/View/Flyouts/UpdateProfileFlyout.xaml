<Controls:Flyout x:Class="Daqifi.Desktop.View.Flyouts.UpdateProfileFlyout"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:Controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
             xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
             xmlns:viewmodels="clr-namespace:Daqifi.Desktop.ViewModels"
              d:DataContext="{d:DesignInstance Type=viewmodels:DaqifiViewModel}"
             mc:Ignorable="d" 
             d:DesignHeight="600" 
             d:DesignWidth="600"
             Width="{Binding FlyoutWidth}"
             Height="{Binding FlyoutHeight}" 
             IsOpen="{Binding IsProfileSettingsOpen, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
             Position="Right" Header="Profile Settings">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <StackPanel Grid.Column="0">
            <!--Profile Name-->
            <GroupBox Header="Profile Name">
                <TextBox x:Name="UpdatedProfileNameLbl" TextChanged="UpdatedProfileNameLblChanged" Text="{Binding SelectedProfile.Name}" Height="30" FontSize="16" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
            </GroupBox>

            <!--Selected Profile Devices-->
            <GroupBox Header="Selected Profile Devices">
                <ListView Name="SelectedDevice" SelectionMode="Multiple"  ItemsSource="{Binding SelectedProfile.Devices}" >
                    <ListView.ItemContainerStyle>
                        <Style TargetType="ListViewItem">
                            <EventSetter Event="PreviewMouseLeftButtonDown" Handler="ListViewItem_PreviewMouseLeftButtonDown" />
                        </Style>
                    </ListView.ItemContainerStyle>
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <Border >
                                <StackPanel Orientation="Horizontal">
                                    <Label Content="{Binding DeviceName}"/>
                                    <Label Content="{Binding DeviceSerialNo}"/>
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </ListView>
            </GroupBox>

            <!--Connected Devices-->
            <GroupBox Header="Connected Devices">
                <ListView Name="AvilableDevices" SelectionMode="Single"  ItemsSource="{Binding ConnectedDevices}" >
                    <ListView.ItemContainerStyle>
                        <Style TargetType="ListViewItem">
                            <EventSetter Event="PreviewMouseLeftButtonDown" Handler="ListViewItem_PreviewMouseLeftButtonDown" />
                        </Style>
                    </ListView.ItemContainerStyle>
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <Border >
                                <StackPanel Orientation="Horizontal">
                                    <Label Content="{Binding Name}"/>
                                    <Label Content="{Binding DeviceSerialNo}"/>
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </ListView>
            </GroupBox>
            <GroupBox Header="Selected Profile Device Frequency">

                <ListView Height="250" Name="SelectedDeviceFrequency" SelectionMode="Multiple"  ScrollViewer.VerticalScrollBarVisibility="Auto"  ItemsSource="{Binding SelectedProfile.Devices}">
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <DockPanel DockPanel.Dock="Top" Margin="0" LastChildFill="True">
                                <StackPanel>
                                    <Label Content="Frequency:" DockPanel.Dock="Left"/>
                                    <Label Content="{Binding DeviceSerialNo}"/>
                                    <Label Content="{Binding DeviceName}"/>
                                </StackPanel>
                                <TextBox Text="{Binding SamplingFrequency}"   HorizontalAlignment="Center" VerticalAlignment="Center" HorizontalContentAlignment="Center" FontSize="20"/>
                                <Slider Margin="5,0,0,0" Tag="{Binding DeviceSerialNo}" ValueChanged ="UpdatedProfileSamplingFrequencyLblvalueChanged"  HorizontalAlignment="Stretch" Minimum="0" Maximum="1000" TickFrequency="1" IsSnapToTickEnabled="True" Value="{Binding SamplingFrequency, Delay=500, UpdateSourceTrigger=PropertyChanged}"/>
                            </DockPanel>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </ListView>

            </GroupBox>
        </StackPanel>
        <StackPanel Grid.Column="1">
            <GroupBox Header="Selected Profile Channels">
                <Grid>
                    <ListView Name="UpdatedProfileChannelList" Height="500" 
                      ItemsSource="{Binding SelectedProfile.Devices}" 
                      Margin="5" Background="Transparent" BorderThickness="0"
                              ScrollViewer.VerticalScrollBarVisibility="Auto">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <Label Content="{Binding DeviceName}"></Label>
                                    <ListView ItemsSource="{Binding Channels}" Height="200" SelectionMode="Multiple" BorderThickness="0" Margin="0" 
                                 ScrollViewer.VerticalScrollBarVisibility="Auto">
                                        <ListView.ItemTemplate>
                                            <DataTemplate>
                                                <Border Margin="5">
                                                    <StackPanel Orientation="Horizontal">
                                                        <Label Content="{Binding Name}"/>
                                                        <Label Content="{Binding Type}"/>
                                                        <Label Content="{Binding SerialNo}"/>
                                                    </StackPanel>
                                                </Border>
                                            </DataTemplate>
                                        </ListView.ItemTemplate>
                                        <ListView.ItemContainerStyle>
                                            <Style TargetType="ListViewItem">
                                                <Setter Property="Padding" Value="0"/>
                                                <Setter Property="Margin" Value="0"/>
                                                <EventSetter Event="PreviewMouseLeftButtonDown" Handler="ListViewItem_PreviewMouseLeftButtonDown"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsChannelActive}" Value="True">
                                                        <Setter Property="IsSelected" Value="True"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ListView.ItemContainerStyle>
                                    </ListView>
                                </StackPanel>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </Grid>
            </GroupBox>
        </StackPanel>
    </Grid>
</Controls:Flyout>
