<Controls:Flyout x:Class="Daqifi.Desktop.View.Flyouts.DevicesFlyout"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:Controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
             xmlns:vm="clr-namespace:Daqifi.Desktop.ViewModels"
             xmlns:networkDataModel="clr-namespace:Daqifi.Desktop.DataModel.Network;assembly=Daqifi.Desktop.DataModel"
             xmlns:sys="clr-namespace:System;assembly=mscorlib" 
             xmlns:controls="http://metro.mahapps.com/winfx/xaml/controls"
             xmlns:local="clr-namespace:Daqifi.Desktop.Converters"
             xmlns:converters="clr-namespace:Daqifi.Desktop.Converters"
             mc:Ignorable="d" 
             d:DataContext="{d:DesignInstance Type=vm:DaqifiViewModel}"
             Position="Right"
             Theme="Dark"
             IsPinned="True"
             Width="{Binding FlyoutWidth}"
             Height="{Binding FlyoutHeight}"
             Background="#1E1E1E"
             IsOpen="{Binding IsDeviceSettingsOpen, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}">
    
    <Controls:Flyout.Resources>
        <!-- Color Constants -->
        <Color x:Key="AccentColor">#4B8FE2</Color>
        <Color x:Key="BackgroundColor">#333333</Color>
        <Color x:Key="HeaderBackgroundColor">#2D2D30</Color>
        
        <!-- Brushes -->
        <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
        <SolidColorBrush x:Key="HeaderBackgroundBrush" Color="{StaticResource HeaderBackgroundColor}"/>
        
        <!-- Data Providers -->
        <ObjectDataProvider MethodName="GetValues" ObjectType="{x:Type sys:Enum}" x:Key="WifiModes">
            <ObjectDataProvider.MethodParameters>
                <x:Type TypeName="networkDataModel:WifiMode" />
            </ObjectDataProvider.MethodParameters>
        </ObjectDataProvider>
        <ObjectDataProvider MethodName="GetValues" ObjectType="{x:Type sys:Enum}" x:Key="WifiSecurityTypes">
            <ObjectDataProvider.MethodParameters>
                <x:Type TypeName="networkDataModel:WifiSecurityType" />
            </ObjectDataProvider.MethodParameters>
        </ObjectDataProvider>
        
        <!-- Converters -->
        <local:BoolToConnectionStatusConverter x:Key="BoolToConnectionStatus"/>
        <local:BoolToStatusColorConverter x:Key="BoolToStatusColor"/>
        <BooleanToVisibilityConverter x:Key="BoolToVis"/>
        <converters:ConnectionTypeToUsbConverter x:Key="ConnectionTypeToUsbConverter"/>
        <local:InvertedBoolToVisibilityConverter x:Key="InvertedBoolToVis" />
        
        <!-- Text Styles -->
        <Style x:Key="HeaderTextBlockStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="SubHeaderTextBlockStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#CCCCCC"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <!-- Control Templates -->
        <ControlTemplate x:Key="ComboBoxToggleTemplate" TargetType="ToggleButton">
            <Border Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition/>
                        <ColumnDefinition Width="30"/>
                    </Grid.ColumnDefinitions>
                    <ContentPresenter Grid.Column="0"
                                    Content="{Binding Path=SelectionBoxItem, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}"
                                    ContentTemplate="{Binding Path=SelectionBoxItemTemplate, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}"
                                    Margin="10,0"
                                    VerticalAlignment="Center"/>
                    <Path Grid.Column="1" 
                          Data="M0,0 L8,8 L16,0" 
                          Stroke="White"
                          StrokeThickness="2"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Center"/>
                </Grid>
            </Border>
            <ControlTemplate.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#404040"/>
                </Trigger>
                <Trigger Property="IsChecked" Value="True">
                    <Setter Property="Background" Value="#404040"/>
                </Trigger>
            </ControlTemplate.Triggers>
        </ControlTemplate>

        <!-- Control Styles -->
        <Style x:Key="TabControlStyle" TargetType="TabControl">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Margin" Value="0"/>
        </Style>

        <Style x:Key="TabItemStyle" TargetType="TabItem">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="#999999"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="MinWidth" Value="125"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border Name="Border" 
                                Background="Transparent"
                                BorderThickness="0,0,0,1" 
                                BorderBrush="{StaticResource BackgroundBrush}"
                                Height="40">
                            <Grid>
                                <Border Name="SelectedBorder"
                                        BorderThickness="0,0,0,2"
                                        BorderBrush="Transparent"
                                        VerticalAlignment="Bottom">
                                    <ContentPresenter x:Name="ContentSite"
                                                    VerticalAlignment="Center"
                                                    HorizontalAlignment="Center"
                                                    ContentSource="Header"
                                                    RecognizesAccessKey="True"/>
                                </Border>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="SelectedBorder" Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
                                <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="False">
                                <Setter Property="Foreground" Value="#999999"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button" BasedOn="{StaticResource MahApps.Styles.Button}">
            <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="MinWidth" Value="80"/>
            <Setter Property="Padding" Value="15,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="AccentButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="PART_Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="2">
                            <VisualStateManager.VisualStateGroups>
                                <VisualStateGroup x:Name="CommonStates">
                                    <VisualState x:Name="Normal"/>
                                    <VisualState x:Name="MouseOver">
                                        <Storyboard>
                                            <!-- Optional: Add slight highlight on hover -->
                                            <ColorAnimation Storyboard.TargetName="PART_Border" 
                                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                                                            To="#5F9EEA" Duration="0:0:0.1"/> 
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="Pressed">
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="PART_Border" 
                                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                                                            To="#3A7BC8" Duration="0:0:0.05"/>
                                        </Storyboard>
                                    </VisualState>
                                    <VisualState x:Name="Disabled">
                                        <Storyboard>
                                            <ColorAnimation Storyboard.TargetName="PART_Border" 
                                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)" 
                                                            To="#666666" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </VisualState>
                                </VisualStateGroup>
                            </VisualStateManager.VisualStateGroups>
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Input Control Styles -->
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,0"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <ToggleButton x:Name="ToggleButton"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                        Template="{StaticResource ComboBoxToggleTemplate}"/>
                            <Popup x:Name="PART_Popup"
                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                   AllowsTransparency="True"
                                   Placement="Bottom"
                                   PopupAnimation="Slide"
                                   PlacementTarget="{Binding ElementName=ToggleButton}">
                                <Border Background="{StaticResource BackgroundBrush}"
                                        BorderThickness="0">
                                    <ScrollViewer MaxHeight="200" 
                                                VerticalScrollBarVisibility="Auto">
                                        <StackPanel IsItemsHost="True"/>
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Resources>
                <Style TargetType="ComboBoxItem">
                    <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="Height" Value="35"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ComboBoxItem">
                                <Border x:Name="Border"
                                        Background="{TemplateBinding Background}"
                                        Padding="10,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <ContentPresenter Grid.Column="0" VerticalAlignment="Center"/>
                                        <TextBlock x:Name="DisabledMessage"
                                                 Grid.Column="1"
                                                 Text="(WiFi Connection - SD Card Not Available)"
                                                 Foreground="#999999"
                                                 FontStyle="Italic"
                                                 VerticalAlignment="Center"
                                                 HorizontalAlignment="Right"
                                                 Margin="10,0,0,0"
                                                 Visibility="Collapsed"/>
                                    </Grid>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#404040"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
                                    </Trigger>
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter Property="Foreground" Value="#999999"/>
                                        <Setter TargetName="DisabledMessage" Property="Visibility" Value="Visible"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Style.Resources>
        </Style>
    </Controls:Flyout.Resources>

    <DockPanel LastChildFill="True">
        <!-- Header with Device Info -->
        <Grid DockPanel.Dock="Top" Background="{StaticResource HeaderBackgroundBrush}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <TextBlock Text="{Binding SelectedDevice.DevicePartNumber}" 
                      FontSize="24" 
                      Foreground="White" 
                      FontWeight="Light"/>
            
            <StackPanel Grid.Row="1" 
                        Orientation="Horizontal" 
                        Margin="0,5,0,0">
                <TextBlock Text="{Binding SelectedDevice.DeviceSerialNo}" 
                          Foreground="#CCCCCC" 
                          FontSize="14"/>
                <TextBlock Text=" | v" 
                          Foreground="#CCCCCC" 
                          FontSize="14" 
                          Margin="10,0"/>
                <TextBlock Text="{Binding SelectedDevice.DeviceVersion}" 
                          Foreground="#CCCCCC" 
                          FontSize="14"/>
                <TextBlock Text=" | " 
                          Foreground="#CCCCCC" 
                          FontSize="14" 
                          Margin="10,0"/>
                <TextBlock Text="{Binding SelectedDevice.IsConnected, Converter={StaticResource BoolToConnectionStatus}}"
                          Foreground="{Binding SelectedDevice.IsConnected, Converter={StaticResource BoolToStatusColor}}"
                          FontSize="14"/>
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <TabControl Style="{StaticResource TabControlStyle}">
            <TabItem Header="DATA ACQUISITION" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <!-- Logging Mode -->
                        <TextBlock Text="LOGGING MODE" 
                                 Style="{StaticResource SectionHeaderStyle}" 
                                 Margin="0,0,0,10"/>
                        <ComboBox Style="{StaticResource ComboBoxStyle}"
                                 Margin="0,0,0,20"
                                 SelectedValue="{Binding SelectedLoggingMode, Mode=TwoWay}"
                                 SelectedValuePath="Content">
                            <ComboBoxItem Content="Stream to App"/>
                            <ComboBoxItem Content="Log to Device" 
                                         IsEnabled="{Binding SelectedDevice.ConnectionType, 
                                                   Converter={StaticResource ConnectionTypeToUsbConverter}}">
                                <ComboBoxItem.ToolTip>
                                    <ToolTip Background="#333333" 
                                            Foreground="White"
                                            BorderBrush="#4B8FE2">
                                        <TextBlock Text="SD Card logging requires a USB connection. WiFi devices do not support SD card logging."
                                                 TextWrapping="Wrap"
                                                 MaxWidth="250"/>
                                    </ToolTip>
                                </ComboBoxItem.ToolTip>
                            </ComboBoxItem>
                        </ComboBox>

                        <!-- Sampling Configuration -->
                        <TextBlock Text="SAMPLING CONFIGURATION" 
                                 Style="{StaticResource SectionHeaderStyle}" 
                                 Margin="0,0,0,10"/>
                        <TextBlock Text="Frequency (Hz)" 
                                 Foreground="White" 
                                 Margin="0,0,0,5"/>
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Text="{Binding ElementName=FrequencySlider, Path=Value, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                    Style="{StaticResource TextBoxStyle}"
                                    Width="80"
                                    FontSize="20"
                                    TextAlignment="Center"
                                    Margin="0,0,10,0"/>
                            <Slider Grid.Column="1"
                                   Name="FrequencySlider"
                                   Minimum="1" 
                                   Maximum="1000"
                                   TickFrequency="1"
                                   IsSnapToTickEnabled="True"
                                   Value="{Binding SelectedDevice.StreamingFrequency, Delay=500, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>

                        <!-- Data Format section removed since we only support Protobuf now -->
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <TabItem Header="NETWORK" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <TextBlock Text="WIFI MODE" 
                                 Style="{StaticResource SectionHeaderStyle}" 
                                 Margin="0,0,0,10"/>
                        <ComboBox Style="{StaticResource ComboBoxStyle}"
                                 Margin="0,0,0,20"
                                 ItemsSource="{Binding Source={StaticResource WifiModes}}"
                                 SelectedItem="{Binding SelectedDevice.NetworkConfiguration.Mode}">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Converter={StaticResource EnumDescription}}"/>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <TextBlock Text="SECURITY TYPE" 
                                 Style="{StaticResource SectionHeaderStyle}" 
                                 Margin="0,0,0,10"/>
                        <ComboBox Style="{StaticResource ComboBoxStyle}"
                                 Margin="0,0,0,20"
                                 ItemsSource="{Binding Source={StaticResource WifiSecurityTypes}}"
                                 SelectedItem="{Binding SelectedDevice.NetworkConfiguration.SecurityType}">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Converter={StaticResource EnumDescription}}"/>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <TextBlock Text="NETWORK SSID" 
                                 Style="{StaticResource SectionHeaderStyle}" 
                                 Margin="0,0,0,10"/>
                        <TextBox Style="{StaticResource TextBoxStyle}"
                                Margin="0,0,0,20"
                                Text="{Binding SelectedDevice.NetworkConfiguration.Ssid, Mode=TwoWay}"/>

                        <TextBlock Text="PASSWORD" 
                                 Style="{StaticResource SectionHeaderStyle}" 
                                 Margin="0,0,0,10"/>
                        <TextBox Style="{StaticResource TextBoxStyle}"
                                Margin="0,0,0,20"
                                Text="{Binding SelectedDevice.NetworkConfiguration.Password, Mode=TwoWay}"/>

                        <Button Content="APPLY NETWORK SETTINGS"
                                Style="{StaticResource AccentButtonStyle}"
                                HorizontalAlignment="Right"
                                Command="{Binding UpdateNetworkConfigurationCommand}"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <TabItem Header="FIRMWARE" Style="{StaticResource TabItemStyle}">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="20">
                        <TextBlock Text="UPDATE DEVICE FIRMWARE" 
                                 Style="{StaticResource SectionHeaderStyle}" 
                                 Margin="0,0,0,15"/>
                        
                        <!-- Firmware Update Controls (Visible only for USB) -->
                        <StackPanel Visibility="{Binding SelectedDeviceSupportsFirmwareUpdate, Converter={StaticResource BoolToVis}}">
                            <DockPanel LastChildFill="True" Margin="0,0,0,15">
                                <Button DockPanel.Dock="Right"
                                        Content="browse"
                                        Style="{StaticResource ButtonStyle}"
                                        Margin="10,0,0,0"
                                        Command="{Binding BrowseForFirmwareCommand}"/>
                                <TextBox Style="{StaticResource TextBoxStyle}"
                                         Text="{Binding FirmwareFilePath, Mode=TwoWay}"/>
                            </DockPanel>

                            <DockPanel LastChildFill="True">
                                <Button DockPanel.Dock="Right"
                                        Content="update"
                                        Style="{StaticResource AccentButtonStyle}"
                                        Margin="10,0,0,0"
                                        Width="80"
                                        Command="{Binding UploadFirmwareCommand}"/>
                                <controls:MetroProgressBar Height="35"
                                                         Background="#333333"
                                                         Foreground="#4B8FE2"
                                                         Minimum="0"
                                                         Maximum="100"
                                                         Value="{Binding UploadFirmwareProgress}"/>
                            </DockPanel>
                        </StackPanel>

                        <!-- Informational Message (Visible only for non-USB) -->
                        <TextBlock Text="Firmware updates require the device to be connected via USB." 
                                   Foreground="#FFCC00" 
                                   Margin="0,10,0,0"
                                   TextWrapping="Wrap"
                                   Visibility="{Binding SelectedDeviceSupportsFirmwareUpdate, Converter={StaticResource InvertedBoolToVis}}"/>

                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </DockPanel>
</Controls:Flyout>
