<Controls:Flyout x:Class="Daqifi.Desktop.View.Flyouts.NotificationsFlyout"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:Controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
             xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
             xmlns:viewmodels="clr-namespace:Daqifi.Desktop.ViewModels"
              d:DataContext="{d:DesignInstance Type=viewmodels:DaqifiViewModel}"
             mc:Ignorable="d" 
             d:DesignHeight="600" 
             d:DesignWidth="300"
             Width="{Binding FlyoutWidth}"
             Height="{Binding FlyoutHeight}" 
             IsOpen="{Binding IsNotificationsOpen, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
             Position="Right" Header="Notifications">
    <Grid>
        <ItemsControl ItemsSource="{Binding NotificationList}">
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <StackPanel Margin="10" Orientation="Vertical">
                        <TextBlock Text="{Binding Message}" FontWeight="Medium" FontSize="18" TextWrapping="Wrap"/>
                        <TextBlock>
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Link}" Value="{x:Null}">
                                            <DataTrigger.Setters>
                                                <Setter Property="Visibility" Value="Collapsed" />
                                            </DataTrigger.Setters>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>

    <Hyperlink NavigateUri="{Binding Link}" RequestNavigate="Hyperlink_RequestNavigate">
        <Run Text="Click here" />
    </Hyperlink>
                        </TextBlock>

                    </StackPanel>
                </DataTemplate>
            </ItemsControl.ItemTemplate>

            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <VirtualizingStackPanel IsVirtualizing="True"
                              VirtualizationMode="Recycling" />
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>

            <ItemsControl.Template>
                <ControlTemplate TargetType="ItemsControl">
                    <Border BorderThickness="{TemplateBinding BorderThickness}"
              BorderBrush="{TemplateBinding BorderBrush}"
              Background="{TemplateBinding Background}">
                        <ScrollViewer CanContentScroll="True" 
                      Padding="{TemplateBinding Padding}"
                      Focusable="False">
                            <ItemsPresenter />
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </ItemsControl.Template>
        </ItemsControl>
    </Grid>
</Controls:Flyout>
