<controls:MetroWindow x:Class="Daqifi.Desktop.View.AddProfileConfirmationDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        xmlns:controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
        Title="Add Profile" ResizeMode="NoResize" Height="200" Width="500"
        GlowBrush="{DynamicResource AccentColorBrush}">
    <Grid>
        <Border>
            <StackPanel Orientation="Vertical" HorizontalAlignment="Stretch"   VerticalAlignment="Center"  Margin="5">
                <Button Content="Save Current Settings" Click="ExistingProfileBtn_Click" Command="{Binding SaveExistingProfileCommand}" FontSize="14" Margin="5" Style="{StaticResource MahApps.Styles.Button.Square.Accent}" Visibility="{Binding SaveProfileExisting, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" controls:ControlsHelper.ContentCharacterCasing="Normal"/>
                <Button Content="Save New Profile" Click="CreateNewProfileBtn_Click" Command="{Binding AddNewProfileCommand}" FontSize="14" Margin="5" Style="{StaticResource MahApps.Styles.Button.Square.Accent}" controls:ControlsHelper.ContentCharacterCasing="Normal" />
            </StackPanel>
        </Border>
    </Grid>
</controls:MetroWindow>
