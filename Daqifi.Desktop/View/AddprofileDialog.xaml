<controls:MetroWindow x:Class="Daqifi.Desktop.View.AddprofileDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Daqifi.Desktop.View"
        xmlns:locals="clr-namespace:Daqifi.Desktop.Helpers"
        mc:Ignorable="d"
        xmlns:controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
        Title="Add Profile" ResizeMode="NoResize" Height="400" Width="500"
                      GlowBrush="{DynamicResource AccentColorBrush}">
    <controls:MetroWindow.Resources>
        <locals:MyMultiValueConverter x:Key="MyMultiValueConverter"/>
    </controls:MetroWindow.Resources>

    <Grid>
        <Border>
            <DockPanel Name="AddProfile" HorizontalAlignment="Stretch"  VerticalAlignment="Stretch" LastChildFill="True" Margin="5">
                <!-- Add Channel Button -->
                <Button Name="btnAdd" DockPanel.Dock="Bottom" IsEnabled="{Binding canAddProfile}"  Command="{Binding AddProfileCommand}" Click="btn_addprofile" Content="Add" Width="100" Height="30" HorizontalAlignment="Right" Style="{StaticResource MahApps.Styles.Button.Square.Accent}" controls:ControlsHelper.ContentCharacterCasing="Normal">
                    <Button.CommandParameter>
                        <MultiBinding Converter="{StaticResource MyMultiValueConverter}" >
                            <Binding ElementName="ChannelList" Path="SelectedItems"/>
                            <Binding ElementName="SelectedDevice" Path="SelectedItems"/>
                        </MultiBinding>
                    </Button.CommandParameter>
                </Button>

                <DockPanel DockPanel.Dock="Top" Margin="5"  LastChildFill="True">
                    <Label Content="Name:" DockPanel.Dock="Left"/>
                    <TextBox Name="ProfileName" Text="{Binding ProfileName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" TextChanged="ProfileName_TextChanged" MaxLength="50"/>
                </DockPanel>

                <!-- Device Selector -->
                <DockPanel DockPanel.Dock="Top" Margin="5" LastChildFill="True">
                    <Label Content="Devices:" DockPanel.Dock="Left"/>
                    <ListView Name="SelectedDevice" SelectedIndex="-1" SelectionMode="Multiple" SelectionChanged="SelectedDevice_SelectionChanged" Loaded="SelectedDevice_Loaded" ItemsSource="{Binding AvailableDevices}">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Border >
                                    <StackPanel Orientation="Vertical">
                                        <Label Content="{Binding Name}"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </DockPanel>

                <!--frequency slider-->
                <DockPanel DockPanel.Dock="Top" Margin="5" LastChildFill="True">
                    <Label Content="Frequency:" DockPanel.Dock="Left"/>
                    <TextBox  Text="{Binding ElementName=FrequencySlider, Path=Value, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" HorizontalAlignment="Center" VerticalAlignment="Center" HorizontalContentAlignment="Center" FontSize="20"/>
                    <Slider  Name="FrequencySlider" ValueChanged="FrequencySlider_ValueChanged" HorizontalAlignment="Stretch" Minimum="1" Maximum="1000" TickFrequency="1" IsSnapToTickEnabled="True" Value="{Binding SelectedStreamingFrequency, Delay=500, UpdateSourceTrigger=PropertyChanged}"/>
                </DockPanel>


                <!-- Channel List -->
                <Grid Margin="5">
                    <ListView Name="ChannelList" ItemsSource="{Binding AvailableChannels}" SelectionChanged="ChannelList_SelectionChanged" SelectionMode="Multiple" Margin="5" Background="Transparent" BorderThickness="0">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Border >
                                    <StackPanel Orientation="Horizontal">
                                        <Label Content="Channel:"/>
                                        <Label Content="{Binding Name}"/>
                                        <Label Content="Type:"/>
                                        <Label Content="{Binding TypeString, Mode=OneWay}"/>
                                        <Label Content="("/>
                                        <Label Content="{Binding DeviceSerialNo}"/>
                                        <Label Content=")"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </Grid>
            </DockPanel>
        </Border>
    </Grid>
</controls:MetroWindow>
