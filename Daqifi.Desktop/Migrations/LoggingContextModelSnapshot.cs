// <auto-generated />
using System;
using Daqifi.Desktop.Logger;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Daqifi.Desktop.Migrations;

[DbContext(typeof(LoggingContext))]
partial class LoggingContextModelSnapshot : ModelSnapshot
{
    protected override void BuildModel(ModelBuilder modelBuilder)
    {
#pragma warning disable 612, 618
        modelBuilder.HasAnnotation("ProductVersion", "8.0.10");

        modelBuilder.Entity("Daqifi.Desktop.Channel.DataSample", b =>
            {
                b.Property<int>("ID")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("INTEGER");

                b.Property<string>("ChannelName")
                    .IsRequired()
                    .HasColumnType("TEXT");

                b.Property<string>("Color")
                    .IsRequired()
                    .HasColumnType("TEXT");

                b.Property<string>("DeviceName")
                    .IsRequired()
                    .HasColumnType("TEXT");

                b.Property<string>("DeviceSerialNo")
                    .IsRequired()
                    .HasColumnType("TEXT");

                b.Property<int>("LoggingSessionID")
                    .HasColumnType("INTEGER");

                b.Property<DateTime>("Timestamp")
                    .HasColumnType("TEXT");

                b.Property<double>("Value")
                    .HasColumnType("REAL");

                b.HasKey("ID");

                b.HasIndex("LoggingSessionID");

                b.ToTable("DataSamples");
            });

        modelBuilder.Entity("Daqifi.Desktop.Loggers.LoggingSession", b =>
            {
                b.Property<int>("ID")
                    .ValueGeneratedOnAdd()
                    .HasColumnType("INTEGER");

                b.Property<string>("DeviceName")
                    .IsRequired()
                    .HasColumnType("TEXT");

                b.Property<DateTime?>("EndTime")
                    .HasColumnType("TEXT");

                b.Property<string>("SessionName")
                    .IsRequired()
                    .HasColumnType("TEXT");

                b.Property<DateTime>("StartTime")
                    .HasColumnType("TEXT");

                b.HasKey("ID");

                b.ToTable("LoggingSessions");
            });

        modelBuilder.Entity("Daqifi.Desktop.Channel.DataSample", b =>
            {
                b.HasOne("Daqifi.Desktop.Loggers.LoggingSession", "LoggingSession")
                    .WithMany()
                    .HasForeignKey("LoggingSessionID")
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired();

                b.Navigation("LoggingSession");
            });
#pragma warning restore 612, 618
    }
}