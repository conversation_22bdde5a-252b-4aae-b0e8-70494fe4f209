<?xml version="1.0" encoding="utf-8"?>
<Application x:Class="Daqifi.Desktop.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"   
             xmlns:helpers="clr-namespace:Daqifi.Desktop.Helpers"
             xmlns:converters="clr-namespace:Daqifi.Desktop.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Fonts.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Themes/Light.Blue.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.FlatSlider.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <Style x:Key="FadeButton" TargetType="{x:Type Button}">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Grid Background="{TemplateBinding Background}">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" Margin="5"/>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Setter Property="Background" Value="White"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="RenderTransformOrigin" Value="0.5, 0.5"/>
                        <Setter Property="RenderTransform">
                            <Setter.Value>
                                <ScaleTransform ScaleX="1.2" ScaleY="1.2"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <BooleanToVisibilityConverter x:Key="BoolToVis"/>
            <helpers:BooleanToVisibilityConverter x:Key="InvertedBoolToVis" True="Collapsed" False="Visible" />
            <helpers:IntToVisibilityConverter x:Key="intToVisibility"/>
            <helpers:EnumDescriptionConverter x:Key="EnumDescription"/>
            <helpers:BooleanToInverseBoolConverter x:Key="BooleanToInverse"/>
            <converters:BoolToActiveStatusConverter x:Key="BoolToActiveStatus"/>
            <converters:BoolToStatusColorConverter x:Key="BoolToStatusColor"/>
            <converters:ListToStringConverter x:Key="ListToStringConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
