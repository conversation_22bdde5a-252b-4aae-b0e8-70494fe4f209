using System.Reflection;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("Daqifi.Desktop.DataModel.Test")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("Daqifi.Desktop.DataModel.Test")]
[assembly: AssemblyCopyright("Copyright ©  2018")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]

[assembly: Guid("51bc8a31-1e5d-4c31-9e5b-8d956fca41c7")]

// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
