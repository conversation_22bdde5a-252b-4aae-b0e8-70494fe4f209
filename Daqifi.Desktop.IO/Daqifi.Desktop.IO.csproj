<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<PropertyGroup>
		<AssemblyVersion>3.0.0.0</AssemblyVersion>
		<FileVersion>3.0.0.0</FileVersion>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Daqifi.Core" Version="0.4.1" />
		<PackageReference Include="Google.Protobuf" Version="3.32.0" />
		<PackageReference Include="Roslynator.Analyzers" Version="4.14.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Daqifi.Desktop.Common\Daqifi.Desktop.Common.csproj" />
		<ProjectReference Include="..\Daqifi.Desktop.DataModel\Daqifi.Desktop.DataModel.csproj" />
	</ItemGroup>
</Project>