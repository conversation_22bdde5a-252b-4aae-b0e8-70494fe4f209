# Build results
[Bb]in/
[Oo]bj/
[Dd]ebug/
[Rr]elease/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]uild/
msbuild.log
msbuild.err
msbuild.wrn

# Visual Studio files
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs
*.[Cc]ache
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.log
*.sarif
*.clef

# Test Results
**/TestResults/
[Tt]est[Rr]esult*/
*.coverage
*.coveragexml

# IDE files
.idea/
.vscode/
.editorconfig

# NuGet Packages
*.nupkg
packages/
*.nuget.props
*.nuget.targets
project.lock.json
project.fragment.lock.json
artifacts/

# Misc
[Tt]emp/
*.tmp
*.swp
*~
._*
.DS_Store
thumbs.db

# Code Coverage Reports
CoverageReport/
