using System.Net;

namespace Daqifi.Desktop.IO.Test.Messages.MessageTypes;

[TestClass]
public class DaqifiOutMessageTests
{
    [TestMethod]
    public void SystemInfoResponse_ValidInput_ParsesCorrectly()
    {
        // Expected result captured from wireshark from DAQiFi Device
        var systemInfoResponse = new byte[]
        {
            0xc0, 0x04, 0x08, 0xa7, 0xca, 0x97, 0x98, 0x07,
            0x48, 0x01, 0x50, 0x0b, 0x80, 0x01, 0x80, 0xe1,
            0xeb, 0x17, 0x88, 0x01, 0x10, 0x90, 0x01, 0x08,
            0xaa, 0x01, 0x02, 0x00, 0x00, 0xb2, 0x01, 0x02,
            0xff, 0xff, 0xba, 0x01, 0x04, 0x00, 0x00, 0xa0,
            0x40, 0xca, 0x01, 0x40, 0x00, 0x00, 0xa0, 0x40,
            0x00, 0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40,
            0x00, 0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40,
            0x00, 0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40,
            0x00, 0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40,
            0x00, 0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40,
            0x00, 0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40,
            0x00, 0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40,
            0x00, 0x00, 0xa0, 0x40, 0xd2, 0x01, 0x20, 0x00,
            0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40, 0x00,
            0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40, 0x00,
            0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40, 0x00,
            0x00, 0xa0, 0x40, 0x00, 0x00, 0xa0, 0x40, 0xd8,
            0x01, 0x80, 0x20, 0xe0, 0x01, 0x80, 0x20, 0xea,
            0x01, 0x40, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0xf2, 0x01, 0x20, 0x00, 0x00, 0x80,
            0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x80,
            0x3f, 0xab, 0xaa, 0x0a, 0x40, 0xab, 0xaa, 0x0a,
            0x40, 0x00, 0x00, 0x80, 0x3f, 0xab, 0xaa, 0x0a,
            0x40, 0x00, 0x00, 0x80, 0x3f, 0xfa, 0x01, 0x40,
            0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f,
            0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f,
            0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f,
            0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f,
            0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f,
            0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f,
            0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f,
            0x00, 0x00, 0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f,
            0x82, 0x02, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x8a, 0x02, 0x20, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x00, 0x00,
            0x80, 0x3f, 0x00, 0x00, 0x80, 0x3f, 0x92, 0x02,
            0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x98, 0x02, 0x10, 0xaa, 0x02, 0x02, 0xf8,
            0xff, 0xda, 0x02, 0x04, 0xc0, 0xa8, 0x00, 0x2d,
            0xe2, 0x02, 0x04, 0xff, 0xff, 0xff, 0x00, 0xea,
            0x02, 0x04, 0xc0, 0xa8, 0x00, 0x01, 0xf2, 0x02,
            0x04, 0xc0, 0xa8, 0x00, 0x01, 0xfa, 0x02, 0x04,
            0xcd, 0xab, 0x02, 0x19, 0x82, 0x03, 0x06, 0x00,
            0x1e, 0xc0, 0x33, 0xb8, 0xbe, 0xba, 0x03, 0x07,
            0x4e, 0x59, 0x51, 0x55, 0x49, 0x53, 0x54, 0xc0,
            0x03, 0xa0, 0x4c, 0xd2, 0x03, 0x0f, 0x43, 0x65,
            0x6e, 0x74, 0x75, 0x72, 0x79, 0x4c, 0x69, 0x6e,
            0x6b, 0x30, 0x36, 0x32, 0x34, 0xe0, 0x03, 0x04,
            0xe8, 0x03, 0x01, 0x92, 0x04, 0x03, 0x4e, 0x71,
            0x31, 0x9a, 0x04, 0x03, 0x31, 0x2e, 0x30, 0xa2,
            0x04, 0x05, 0x31, 0x2e, 0x30, 0x2e, 0x33, 0xa8,
            0x04, 0x9d, 0xc9, 0x84, 0xe2, 0x8e, 0x82, 0xd5,
            0xf0, 0x24
        };

        Stream stream = new MemoryStream(systemInfoResponse);
        var protobufMessage = DaqifiOutMessage.Parser.ParseDelimitedFrom(stream);
        
        Assert.AreEqual("NYQUIST", protobufMessage.HostName);
        Assert.AreEqual((uint)4, protobufMessage.WifiSecurityMode);
        Assert.AreEqual((uint)9760, protobufMessage.DevicePort);
        Assert.AreEqual("CenturyLink0624", protobufMessage.Ssid);
        Assert.AreEqual("************", new IPAddress(protobufMessage.IpAddr.ToByteArray()).ToString());
        Assert.AreEqual("Nq1", protobufMessage.DevicePn);
        Assert.AreEqual("1.0.3", protobufMessage.DeviceFwRev);
    }
}