# A GlobalConfig file for the 'Default' MSTest analysis mode.

is_global = true
global_level = -100

# MSTEST0001: Explicitly enable or disable tests parallelization
dotnet_diagnostic.MSTEST0001.severity = suggestion

# MSTEST0002: Test classes should have valid layout
dotnet_diagnostic.MSTEST0002.severity = warning

# MSTEST0003: Test methods should have valid layout
dotnet_diagnostic.MSTEST0003.severity = warning

# MSTEST0004: Public types should be test classes
dotnet_diagnostic.MSTEST0004.severity = none

# MSTEST0005: Test context property should have valid layout
dotnet_diagnostic.MSTEST0005.severity = warning

# MSTEST0006: Avoid '[ExpectedException]'
dotnet_diagnostic.MSTEST0006.severity = warning

# MSTEST0007: [Owner] can only be set on methods marked with [TestMethod]
dotnet_diagnostic.MSTEST0007.severity = suggestion

# MSTEST0008: TestInitialize method should have valid layout
dotnet_diagnostic.MSTEST0008.severity = warning

# MSTEST0009: TestCleanup method should have valid layout
dotnet_diagnostic.MSTEST0009.severity = warning

# MSTEST0010: ClassInitialize methods should have valid layout
dotnet_diagnostic.MSTEST0010.severity = warning

# MSTEST0011: ClassCleanup methods should have valid layout
dotnet_diagnostic.MSTEST0011.severity = warning

# MSTEST0012: AssemblyInitialize methods should have valid layout
dotnet_diagnostic.MSTEST0012.severity = warning

# MSTEST0013: AssemblyCleanup methods should have valid layout
dotnet_diagnostic.MSTEST0013.severity = warning

# MSTEST0014: DataRow should be valid
dotnet_diagnostic.MSTEST0014.severity = warning

# MSTEST0015: Test method should not be ignored
dotnet_diagnostic.MSTEST0015.severity = none

# MSTEST0016: Test class should have test method
dotnet_diagnostic.MSTEST0016.severity = suggestion

# MSTEST0017: Assertion arguments should be passed in the correct order
dotnet_diagnostic.MSTEST0017.severity = suggestion

# MSTEST0018: DynamicData should be valid
dotnet_diagnostic.MSTEST0018.severity = warning

# MSTEST0019: Prefer TestInitialize methods over constructors
dotnet_diagnostic.MSTEST0019.severity = none

# MSTEST0020: Prefer constructors over TestInitialize methods
dotnet_diagnostic.MSTEST0020.severity = none

# MSTEST0021: Prefer 'Dispose' over TestCleanup methods
dotnet_diagnostic.MSTEST0021.severity = none

# MSTEST0022: Prefer TestCleanup over 'Dispose' methods
dotnet_diagnostic.MSTEST0022.severity = none

# MSTEST0023: Do not negate boolean assertions
dotnet_diagnostic.MSTEST0023.severity = suggestion

# MSTEST0024: Do not store TestContext in a static member
dotnet_diagnostic.MSTEST0024.severity = suggestion

# MSTEST0025: Use 'Assert.Fail' instead of an always-failing assert
dotnet_diagnostic.MSTEST0025.severity = suggestion

# MSTEST0026: Avoid conditional access in assertions
dotnet_diagnostic.MSTEST0026.severity = none

# MSTEST0029: Public methods should be test methods
dotnet_diagnostic.MSTEST0029.severity = none

# MSTEST0030: Type containing '[TestMethod]' should be marked with '[TestClass]'
dotnet_diagnostic.MSTEST0030.severity = suggestion

# MSTEST0031: 'System.ComponentModel.DescriptionAttribute' has no effect on test methods
dotnet_diagnostic.MSTEST0031.severity = suggestion

# MSTEST0032: Assertion condition is always true
dotnet_diagnostic.MSTEST0032.severity = suggestion

# MSTEST0034: Use 'ClassCleanupBehavior.EndOfClass' with the '[ClassCleanup]'
dotnet_diagnostic.MSTEST0034.severity = suggestion

# MSTEST0035: '[DeploymentItem]' can be specified only on test class or test method
dotnet_diagnostic.MSTEST0035.severity = suggestion

# MSTEST0036: Do not use shadowing
dotnet_diagnostic.MSTEST0036.severity = warning

# MSTEST0037: Use proper 'Assert' methods
dotnet_diagnostic.MSTEST0037.severity = suggestion

# MSTEST0038: Don't use 'Assert.AreSame' or 'Assert.AreNotSame' with value types
dotnet_diagnostic.MSTEST0038.severity = warning

# MSTEST0039: Use newer methods to assert exceptions
dotnet_diagnostic.MSTEST0039.severity = warning

# MSTEST0040: Do not assert inside 'async void' contexts
dotnet_diagnostic.MSTEST0040.severity = warning

# MSTEST0041: Use 'ConditionBaseAttribute' on test classes
dotnet_diagnostic.MSTEST0041.severity = warning

# MSTEST0042: Avoid duplicated 'DataRow' entries
dotnet_diagnostic.MSTEST0042.severity = warning

# MSTEST0043: Use retry attribute on test method
dotnet_diagnostic.MSTEST0043.severity = warning

# MSTEST0044: Prefer 'TestMethod' over 'DataTestMethod'
dotnet_diagnostic.MSTEST0044.severity = warning

# MSTEST0045: Use 'CooperativeCancellation = true' with '[Timeout]'
dotnet_diagnostic.MSTEST0045.severity = suggestion

# MSTEST0046: Use 'Assert' instead of 'StringAssert'
dotnet_diagnostic.MSTEST0046.severity = suggestion

# MSTEST0048: TestContext property cannot be accessed in this context
dotnet_diagnostic.MSTEST0048.severity = warning

# MSTEST0049: Flow TestContext.CancellationToken to async operations
dotnet_diagnostic.MSTEST0049.severity = suggestion

# MSTEST0050: GlobalTestInitialize and GlobalTestCleanup methods should have valid layout
dotnet_diagnostic.MSTEST0050.severity = error

