<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<PropertyGroup>
		<AssemblyVersion>3.0.0.0</AssemblyVersion>
		<FileVersion>3.0.0.0</FileVersion>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Bugsnag.AspNet.Core" Version="4.1.0" />
		<PackageReference Include="NLog" Version="6.0.3" />
		<PackageReference Include="Roslynator.Analyzers" Version="4.14.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

</Project>
