name: Prebuild

on:
  pull_request:
    types: [opened, synchronize, reopened, edited]

jobs:
  validate-pr-title:
    runs-on: ubuntu-latest
    steps:
      - name: PR Conventional Commit Validation
        id: check_title
        uses: ytanikin/PRConventionalCommits@1.3.0
        with:
          task_types: '["feat","fix","docs", "deps", "chore"]'
          add_label: 'false'

      - name: Leave a comment if PR title is invalid
        if: ${{ failure() }}
        uses: actions/github-script@v7
        with:
          script: |
            const prNumber = context.payload.pull_request.number;
            const acceptablePrefixes = ['feat', 'fix', 'docs', 'deps', 'chore'];
            const commentIdentifier = '<!-- conventional-commits-comment -->';
            const newBody = `${commentIdentifier}\n❌ The PR title does not follow the Conventional Commits format. Please use one of the following prefixes: ${acceptablePrefixes.join(', ')}. For example: \`feat: add new feature\``;

            // Get existing comments
            const { data: existingComments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber,
            });

            // Find previous bot comment
            let botComment = existingComments.find(comment =>
              comment.user.login === 'github-actions[bot]' && comment.body.includes(commentIdentifier)
            );

            if (botComment) {
              // Update the existing comment
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id,
                body: newBody,
              });
            } else {
              // Create a new comment
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prNumber,
                body: newBody,
              });
            }

      - name: Remove comment if PR title is valid
        if: ${{ success() }}
        uses: actions/github-script@v7
        with:
          script: |
            const prNumber = context.payload.pull_request.number;
            const commentIdentifier = '<!-- conventional-commits-comment -->';

            // Get existing comments
            const { data: existingComments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber,
            });

            // Find previous bot comment
            let botComment = existingComments.find(comment =>
              comment.user.login === 'github-actions[bot]' && comment.body.includes(commentIdentifier)
            );

            if (botComment) {
              // Delete the existing comment
              await github.rest.issues.deleteComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id,
              });
            }
