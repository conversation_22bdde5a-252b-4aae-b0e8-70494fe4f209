name: Build

on:
  pull_request:

jobs:
  build-and-test:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v5

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 9.0.x

    - name: Restore dependencies
      run: dotnet restore

    - name: Build with Analysis
      run: dotnet build --no-restore --configuration Release /p:EnforceCodeStyleInBuild=true

    - name: Publish app (framework-dependent)
      run: dotnet publish Daqifi.Desktop/Daqifi.Desktop.csproj -c Release --no-restore

    - name: Test with Coverage
      run: dotnet test --no-build --verbosity normal --configuration Release --collect:"XPlat Code Coverage" --results-directory ./TestResults/ /p:CoverletOutputFormat=cobertura

    - name: Install ReportGenerator
      run: dotnet tool install -g dotnet-reportgenerator-globaltool

    - name: Generate Coverage Report
      run: reportgenerator -reports:"./TestResults/**/coverage.cobertura.xml" -targetdir:"./CoverageReport" -reporttypes:"HtmlInline_AzurePipelines;TextSummary;MarkdownSummaryGithub" -verbosity:Warning

    - name: Upload Coverage Report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: ./CoverageReport/
        if-no-files-found: warn

    - name: Add Coverage to Step Summary
      if: github.event_name == 'pull_request'
      run: |
        if (Test-Path "./CoverageReport/SummaryGithub.md") {
          $summary = Get-Content "./CoverageReport/SummaryGithub.md" -Raw
          echo "## Code Coverage Report" >> $env:GITHUB_STEP_SUMMARY
          echo "$summary" >> $env:GITHUB_STEP_SUMMARY
        }
      shell: pwsh

    - name: Comment Coverage on PR
      if: github.event_name == 'pull_request'
      run: |
        if (Test-Path "./CoverageReport/SummaryGithub.md") {
          $summary = Get-Content "./CoverageReport/SummaryGithub.md" -Raw
          $commentBody = @"
        ## 📊 Code Coverage Report
        
        $summary
        
        ---
        *Coverage report generated by [ReportGenerator](https://github.com/danielpalme/ReportGenerator) • [View full report in build artifacts](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})*
        "@
          
          # Clean up any existing coverage comments first
          Write-Host "Cleaning up existing coverage comments..."
          $existingComments = gh api repos/${{ github.repository }}/issues/${{ github.event.number }}/comments --jq '.[] | select(.user.login == "github-actions[bot]" and (.body | contains("📊 Code Coverage Report"))) | .id'
          
          if ($existingComments) {
            $commentIds = $existingComments -split "`n" | Where-Object { $_.Trim() -ne "" }
            foreach ($commentId in $commentIds) {
              Write-Host "Deleting existing coverage comment: $commentId"
              gh api repos/${{ github.repository }}/issues/comments/$commentId --method DELETE
            }
            Write-Host "Cleaned up $($commentIds.Count) existing coverage comment(s)"
          }
          
          # Create new comment
          Write-Host "Creating new coverage comment"
          $commentBody | gh pr comment ${{ github.event.number }} --body-file -
        }
      shell: pwsh
      env:
        GH_TOKEN: ${{ github.token }}

    - name: Build MSI Installer
      run: |
        cd Daqifi.Desktop.Setup
        dotnet build -c Release

    - name: Upload MSI Artifact
      uses: actions/upload-artifact@v4
      with:
        name: DAQifiDesktop-Installer
        path: Daqifi.Desktop.Setup/DAQifiDesktopSetup/bin/x86/Release/DAQifiDesktop_Setup.msi
        if-no-files-found: error
