using System.Reflection;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("Daqifi.Desktop.Common.Test")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("Daqifi.Desktop.Common.Test")]
[assembly: AssemblyCopyright("Copyright ©  2018")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]

[assembly: Guid("df1457a3-fe50-4260-8f0d-a30099514470")]

// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
